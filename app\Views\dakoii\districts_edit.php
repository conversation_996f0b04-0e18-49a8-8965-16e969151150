<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> Edit District</h5>
                    <a href="<?= base_url('location-management/districts/' . $district['province_id']) ?>" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to Districts
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('location-management/districts/' . $district['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="form-group">
                            <label for="province_id">Province <span class="text-danger">*</span></label>
                            <select class="form-control" id="province_id" name="province_id" required>
                                <option value="">Select Province</option>
                                <?php foreach ($provinces as $prov): ?>
                                    <option value="<?= $prov['id'] ?>" 
                                            <?= old('province_id', $district['province_id']) == $prov['id'] ? 'selected' : '' ?>>
                                        <?= esc($prov['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a province.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="name">District Name <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="<?= old('name', $district['name']) ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid district name.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="districtcode">District Code <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="districtcode" 
                                   name="districtcode" 
                                   value="<?= old('districtcode', $district['districtcode']) ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid district code.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="json_id">JSON ID</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="json_id" 
                                   name="json_id" 
                                   value="<?= old('json_id', $district['json_id']) ?>">
                            <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update District
                            </button>
                            <a href="<?= base_url('location-management/districts/' . $district['province_id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Country:</strong> <?= esc($set_country['name']) ?></p>
                    <p><strong>Current Province:</strong> <?= esc($province['name']) ?></p>
                    <p><strong>District ID:</strong> <?= $district['id'] ?></p>
                    <p><strong>Created:</strong> <?= date('M d, Y', strtotime($district['created_at'])) ?></p>
                    <hr>
                    <h6>Guidelines:</h6>
                    <ul class="small">
                        <li>District name must be at least 3 characters long</li>
                        <li>District code must be unique</li>
                        <li>Must belong to a province</li>
                        <li>JSON ID is optional and used for mapping purposes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection(); ?>
